- const { darkmode } = theme
nav#nav
  #nav-group
    span#blog_name
      if theme.nav.enable
        .back-home-button
          i.anzhiyufont.anzhiyu-icon-grip-vertical
          .back-menu-list-groups
            each group in theme.nav.menu
              .back-menu-list-group
                .back-menu-list-title= group.title
                .back-menu-list
                  each item in group.item
                    a.back-menu-item(href=url_for(item.link), title=item.name)
                      img.back-menu-item-icon(src=item.icon alt=item.name)
                      span.back-menu-item-text= item.name
      a#site-name(href=url_for('/') accesskey="h")
        .title #[=config.title]
        i.anzhiyufont.anzhiyu-icon-house-chimney
      if (theme.nav.clock)
        !=partial('includes/anzhiyu/clock', {}, {cache: true})

    div.mask-name-container
      #name-container
        a#page-name(href="javascript:anzhiyu.scrollToDest(0, 500)") PAGE_NAME

    #menus
      !=partial('includes/header/menu_item', {}, {cache: true})
    #nav-right
      if theme.nav.travelling
        .nav-button.only-home#travellings_button(title='随机前往一个开往项目网站')
          a.site-page(onclick='anzhiyu.totraveling()', title='随机前往一个开往项目网站', href='javascript:void(0);', rel='external nofollow', data-pjax-state='external')
            i.anzhiyufont.anzhiyu-icon-train
      .nav-button#randomPost_button
        a.site-page(onclick='toRandomPost()', title='随机前往一个文章', href='javascript:void(0);')
          i.anzhiyufont.anzhiyu-icon-dice
      if (theme.algolia_search.enable || theme.local_search.enable || theme.docsearch.enable)
        div.nav-button#search-button
          a.site-page.social-icon.search(href='javascript:void(0);', title='搜索🔍' accesskey="s")
            i.anzhiyufont.anzhiyu-icon-magnifying-glass
            span=' '+_p('search.title')

      if theme.centerConsole.enable
        input#center-console(type="checkbox")
        label.widget(for="center-console" title=_p("中控台") onclick="anzhiyu.switchConsole();")
          i.left
          i.widget.center
          i.widget.right

      !=partial('includes/anzhiyu/console', {}, {cache:true})

      div.nav-button#nav-totop
        a.totopbtn(href='javascript:void(0);')
          i.anzhiyufont.anzhiyu-icon-arrow-up
          span#percent(onclick="anzhiyu.scrollToDest(0,500)") 0

      #toggle-menu
        a.site-page(href='javascript:void(0);' title="切换")
          i.anzhiyufont.anzhiyu-icon-bars


