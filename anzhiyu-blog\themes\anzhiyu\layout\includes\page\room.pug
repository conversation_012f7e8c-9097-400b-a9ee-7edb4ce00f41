#room_page
	div.house#h 
			div.h-lights
					div.h-light
					div.h-light
					div.h-light
					div.h-light
					div.h-light
					div.h-light
							
			div.h-shadow
			//---------------------
			div.alt
					div.alt__front.face 
					div.alt__back.face 
					div.alt__right.face 
					div.alt__left.face 
					div.alt__top.face 
							div.light
							div.light
							div.light
							div.light
							div.light
							div.light
							div.light
							div.light
							div.light
					div.alt__bottom.face 
			div.alb
					div.alb__front.face 
					div.alb__back.face 
					div.alb__right.face 
					div.alb__left.face 
					div.alb__top.face 
					div.alb__bottom.face 
			div.arb
					div.arb__front.face 
					div.arb__back.face 
					div.arb__right.face 
					div.arb__left.face 
					div.arb__top.face 
					div.arb__bottom.face 
			//---------------------
			div.blt
					div.blt__front.face 
					div.blt__back.face 
					div.blt__right.face 
					div.blt__left.face 
					div.blt__top.face 
					div.blt__bottom.face 
			div.blt2
					div.blt2__front.face 
					div.blt2__back.face 
					div.blt2__right.face 
					div.blt2__left.face 
					div.blt2__top.face 
					div.blt2__bottom.face 
			div.blb
					div.blb__front.face 
					div.blb__back.face 
					div.blb__right.face 
					div.blb__left.face 
					div.blb__top.face 
					div.blb__bottom.face 
			div.blb2
					div.blb2__front.face 
					div.blb2__back.face 
					div.blb2__right.face 
					div.blb2__left.face 
					div.blb2__top.face 
					div.blb2__bottom.face 
			//---------------------
			div.puerta-c
					div.puerta
							div.puerta__front.face 
							div.puerta__back.face 
							div.puerta__right.face 
							div.puerta__left.face 
							div.puerta__top.face 
							div.puerta__bottom.face 
					div.puerta-l
							div.puerta-l__front.face 
							div.puerta-l__back.face 
							div.puerta-l__right.face 
							div.puerta-l__left.face 
							div.puerta-l__top.face 
							div.puerta-l__bottom.face 
					div.puerta-r
							div.puerta-r__front.face 
							div.puerta-r__back.face 
							div.puerta-r__right.face 
							div.puerta-r__left.face 
							div.puerta-r__top.face 
							div.puerta-r__bottom.face 
					div.puerta-t
							div.puerta-t__front.face 
							div.puerta-t__back.face 
							div.puerta-t__right.face 
							div.puerta-t__left.face 
							div.puerta-t__top.face 
							div.puerta-t__bottom.face 
			div.cuadro-l
					div.cuadro-l__front.face 
					div.cuadro-l__back.face 
					div.cuadro-l__right.face 
					div.cuadro-l__left.face 
					div.cuadro-l__top.face 
					div.cuadro-l__bottom.face 
			div.cuadro-r
					div.cuadro-r__front.face 
					div.cuadro-r__back.face 
					div.cuadro-r__right.face 
					div.cuadro-r__left.face 
					div.cuadro-r__top.face 
					div.cuadro-r__bottom.face 
			div.librero
					div.librero__front.face 
					div.librero__back.face 
					div.librero__right.face 
					div.librero__left.face 
					div.librero__top.face 
					div.librero__bottom.face     
			div.libros  
					- let lb = 0
					while lb < 6    
							div.libro
									div.libro__front.face 
									div.libro__back.face 
									div.libro__right.face 
									div.libro__left.face 
									div.libro__top.face 
									div.libro__bottom.face 
							- lb++
			div.fotos 
					- let ft = 0
					while ft < 2    
							div.foto
									div.foto__front.face 
									div.foto__back.face 
									div.foto__right.face 
									div.foto__left.face 
									div.foto__top.face 
									div.foto__bottom.face 
							- ft++
			div.cajas 
					- let cj = 0
					while cj < 3    
							div.caja
									div.caja__front.face 
									div.caja__back.face 
									div.caja__right.face 
									div.caja__left.face 
									div.caja__top.face 
									div.caja__bottom.face 
							- cj++
			div.tv
					div.tv__front.face 
					div.tv__back.face 
					div.tv__right.face 
					div.tv__left.face 
					div.tv__top.face 
					div.tv__bottom.face 
			div.repisa-t
					div.repisa-t__front.face 
					div.repisa-t__back.face 
					div.repisa-t__right.face 
					div.repisa-t__left.face 
					div.repisa-t__top.face 
					div.repisa-t__bottom.face 
			div.repisa-b
					div.repisa-b__front.face 
					div.repisa-b__back.face 
					div.repisa-b__right.face 
					div.repisa-b__left.face 
					div.repisa-b__top.face 
					div.repisa-b__bottom.face 
			div.bocina-l
					div.bocina-l__front.face 
					div.bocina-l__back.face 
					div.bocina-l__right.face 
					div.bocina-l__left.face 
					div.bocina-l__top.face 
					div.bocina-l__bottom.face 
			div.bocina-r
					div.bocina-r__front.face 
					div.bocina-r__back.face 
					div.bocina-r__right.face 
					div.bocina-r__left.face 
					div.bocina-r__top.face 
					div.bocina-r__bottom.face 
			div.muro
					div.muro__front.face 
					div.muro__back.face 
					div.muro__right.face 
					div.muro__left.face 
					div.muro__top.face 
					div.muro__bottom.face 
			div.sillon-c
					div.sillon-b
							div.sillon-b__front.face 
							div.sillon-b__back.face 
							div.sillon-b__right.face 
							div.sillon-b__left.face 
							div.sillon-b__top.face 
							div.sillon-b__bottom.face 
					div.sillon-t
							div.sillon-t__front.face 
							div.sillon-t__back.face 
							div.sillon-t__right.face 
							div.sillon-t__left.face 
							div.sillon-t__top.face 
							div.sillon-t__bottom.face 
					div.sillon-l
							div.sillon-l__front.face 
							div.sillon-l__back.face 
							div.sillon-l__right.face 
							div.sillon-l__left.face 
							div.sillon-l__top.face 
							div.sillon-l__bottom.face 
					div.sillon-r
							div.sillon-r__front.face 
							div.sillon-r__back.face 
							div.sillon-r__right.face 
							div.sillon-r__left.face 
							div.sillon-r__top.face 
							div.sillon-r__bottom.face 
			div.mesa-c
					div.mesa
							div.mesa__front.face 
							div.mesa__back.face 
							div.mesa__right.face 
							div.mesa__left.face 
							div.mesa__top.face 
							div.mesa__bottom.face 
							
					- let np = 0
					while np < 4        
							div.mesa-p
									div.mesa-p__front.face 
									div.mesa-p__back.face 
									div.mesa-p__right.face 
									div.mesa-p__left.face 
									div.mesa-p__top.face 
									div.mesa-p__bottom.face 
							- np++
					
					div.mesa-shadow
			div.tablet
					div.tablet__front.face 
					div.tablet__back.face 
					div.tablet__right.face 
					div.tablet__left.face 
					div.tablet__top.face 
					div.tablet__bottom.face

	.description
		span 哇,被你发现了,这里是我的小房间，要一起玩游戏🎮嘛
		.banner-button-group
			a.banner-button(onclick='pjax.loadUrl("/about/")')
				i.anzhiyufont.anzhiyu-icon-arrow-circle-right(style='font-size: 1.5rem')
				span.banner-button-text 立刻玩
	script(async).
		const h = document.querySelector("#h");
		const b = document.body;

		let base = e => {
			var x = e.pageX / window.innerWidth - 0.5;
			var y = e.pageY / window.innerHeight - 0.5;
			h.style.transform = `
						perspective(90vw)
						rotateX(${y * 4 + 75}deg)
						rotateZ(${-x * 12 + 45}deg)
						translateZ(-9vw)
						translateX(-50%)
				`;
		};

		b.addEventListener("pointermove", base);