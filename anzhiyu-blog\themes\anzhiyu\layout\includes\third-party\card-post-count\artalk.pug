- const { server, site } = theme.artalk

script.
  (() => {
    const getArtalkCount = () => {
      const runWidget = () => {
        Artalk.loadCountWidget({
          server: '!{server}',
          site: '!{site}',
          countEl: '.artalk-count'
        })
      }

      if (typeof Artalk === 'function') runWidget()
      else getScript('!{theme.asset.artalk_js}').then(runWidget)
    }

    window.pjax ? getArtalkCount() : window.addEventListener('load', getArtalkCount)
  })()