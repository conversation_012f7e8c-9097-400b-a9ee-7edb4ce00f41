-
  var options = {
    prev_text: '<i class="anzhiyufont anzhiyu-icon-chevron-left fa-fw" style="font-weight: inherit; font-size: 1rem;"></i><div class="pagination_tips_prev">上页</div>',
    next_text: '<div class="pagination_tips_next">下页</div><i style="font-weight: inherit; font-size: 1rem;" class="anzhiyufont anzhiyu-icon-chevron-right fa-fw"></i>',
    mid_size: 1,
    escape: false
  }

if is_post()
  - let prev = theme.post_pagination === 1 || theme.post_pagination === 3 || theme.post_pagination === 4 ? page.prev : page.next
  - let next = theme.post_pagination === 1 || theme.post_pagination === 3 || theme.post_pagination === 4 ? page.next : page.prev
  nav#pagination.pagination-post
    if(prev)
      - var hasPageNext = next ? 'pull-left' : 'pull-full'
      .prev-post(class=hasPageNext)
        - var pagination_cover = prev.cover === false ? prev.randomcover : prev.cover
        a(href=url_for(prev.path))
          img.prev-cover(src=url_for(pagination_cover) onerror=`onerror=null;src='${url_for(theme.error_img.post_page)}'` alt='cover of previous post')
          .pagination-info
            .label=_p('pagination.prev')
            .prev_info=prev.title
          
    if(next)
      - var hasPagePrev = prev ? 'pull-right' : 'pull-full'
      - var pagination_cover = next.cover == false ? next.randomcover : next.cover
      .next-post(class=hasPagePrev)
        a(href=url_for(next.path))
          img.next-cover(src=url_for(pagination_cover) onerror=`onerror=null;src='${url_for(theme.error_img.post_page)}'` alt='cover of next post')
          .pagination-info
            .label=_p('pagination.next')
            .next_info=next.title
else
  nav#pagination
    .pagination
      if is_home()
        - options.format = 'page/%d/#content-inner'
      !=paginator(options)
      if is_home()
        div(class="toPageGroup")
          input(id="toPageText" oninput="value=value.replace(/[^0-9]/g,'')" maxlength="3" onkeyup="this.value=this.value.replace(/[^u4e00-u9fa5w]/g,'')" aria-label="toPage")
          a(id="toPageButton" onclick="anzhiyu.toPage()")
            i.anzhiyufont.anzhiyu-icon-angles-right(style="font-weight: inherit; font-size: 1rem;")