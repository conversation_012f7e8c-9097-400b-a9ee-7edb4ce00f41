if $highlight_theme != false
  @require 'diff'

#article-container
  figure.highlight
    .line
      if wordWrap
        &:before
          display: inline-block
          padding: 0 6px 0 0
          min-width: 30px
          color: var(--hlnumber-color)
          content: counter(line)
          counter-increment: line
          text-align: left

      &.marked
        background-color: $highlight-selection

    table
      display: block
      overflow: auto
      border: none

      td
        padding: 0
        border: none

    .gutter pre
      padding-right: .625rem
      padding-left: 1.25rem
      if $highlight_theme == 'light' || ($highlight_theme == 'mac light')
        background: var(--anzhiyu-secondbg)
        background-color: var(--anzhiyu-secondbg)
        border-right: var(--style-border-always);
        padding-right: .5rem
        padding-left: .5rem
      else
        background: var(--hlexpand-bg)
      color: var(--hlnumber-color)
      text-align: right

    .code pre
      padding-right: 10px
      padding-left: 10px
      width: 100%
