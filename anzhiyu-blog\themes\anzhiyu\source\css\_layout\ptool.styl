if hexo-config('ptool.enable') == true
  .post-tools
    display: flex;
    width: 100%;
    justify-content: space-between;
    flex-wrap: wrap;
    -webkit-user-select: none;
    flex-direction: column;
    align-items: center;
    .post-tools-left
      white-space: nowrap;
      display: flex;
      text-overflow: ellipsis;
      flex-wrap: wrap;
      margin-bottom: 0.5rem;
      justify-content: center;
      .rewardLeftButton, .shareRight
        display: flex
        margin: 10px
      +maxWidth768()
        margin-top: 1rem;
        justify-content: center;
        width: 100%;
        > div
          margin: 8px;

.post-tools-right
  padding-right: 0px;
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  +maxWidth768()
    flex-direction: column;
.share-link
  margin-left: 0.5rem;
  display: flex;
  justify-content: center;
  position: relative;
  text-align: center;
  &.mobile:hover 
    .share-button
      background: var(--anzhiyu-green);
      border-color: var(--anzhiyu-green);
      box-shadow: var(--anzhiyu-shadow-green);
      color: var(--anzhiyu-white);
    .share-main
      opacity: 1;
      transform: translateY(0);


  .share-qrcode
    display: flex;
    justify-content: center;
  .share-button
    background: var(--anzhiyu-card-bg);
    border: var(--style-border);
    color: var(--anzhiyu-fontcolor);
    border-radius: 8px;
    cursor: pointer;
    transition: 0.3s;
    width: 56px;
    height: 40px;
    &:hover
      background: var(--anzhiyu-main);
      border-color: var(--anzhiyu-main);
      color: var(--anzhiyu-white);
      transition: 0.3s;
      box-shadow: var(--anzhiyu-shadow-blue);

    i
      line-height: 38px;
      font-size: 1.1rem;
  .share-main
    position: absolute;
    bottom: 40px;
    z-index: 100;
    padding-bottom: 15px;
    opacity: 0;
    pointer-events: none;
    transition: 0.3s;
    transform: translateY(8px);
    .share-main-all
      padding: 12px;
      border-radius: 12px;
      background: var(--anzhiyu-background);
      animation: donate_effcet 0.3s 0.1s ease both;
      flex-direction: column;
      border: var(--style-border-always);
      #qrcode 
        width: 150px;
        height: 150px;
        min-width: 150px;
        min-height: 150px;
        background: var(--anzhiyu-white);
        padding: 8px;
        border-radius: 8px;
        margin-bottom: 8px;
        border: var(--style-border-always);
      .reward-dec 
        font-size: 12px;

if hexo-config('ptool.enable') == true && !hexo-config('ptool.mode')
  .post-tools .post-tools-left > div
    margin: 0
  .post-tools .post-tools-left
    width auto
if hexo-config('ptool.enable') == true && hexo-config('ptool.categories')
  #post .tag_share
    margin-bottom: 2rem
    flex-direction: column;