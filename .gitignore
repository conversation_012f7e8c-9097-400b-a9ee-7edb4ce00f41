# Hexo博客项目 .gitignore

# 依赖文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 生成的静态文件
anzhiyu-blog/public/
anzhiyu-blog/.deploy_git/

# 数据库文件
anzhiyu-blog/db.json

# 日志文件
*.log

# 临时文件
.tmp/
.cache/

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# IDE文件
.vscode/
.idea/
*.swp
*.swo

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 压缩包
*.zip
*.tar.gz
*.rar

# 备份文件
*.bak
*.backup

# Hexo特定忽略
anzhiyu-blog/.deploy*/
anzhiyu-blog/source/_drafts/
