each name in theme.comments.use
  case name
    when 'Valine'
      !=partial('includes/third-party/comments/valine', {}, {cache: true})
    when 'Twikoo'
      !=partial('includes/third-party/comments/twikoo', {}, {cache: true})
    when 'Waline'
      !=partial('includes/third-party/comments/waline', {}, {cache: true})
    when 'Artalk'
      !=partial('includes/third-party/comments/artalk', {}, {cache: true})
    when 'Giscus'
      !=partial('includes/third-party/comments/giscus', {}, {cache: true})