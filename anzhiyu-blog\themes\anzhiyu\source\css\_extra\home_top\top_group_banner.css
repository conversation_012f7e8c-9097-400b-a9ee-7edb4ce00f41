.todayCard-title,
.todayCard-tips,
.topGroup .banner-button {
  color: #ffffff;
}

.topGroup {
  height: 340px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: calc(600px + 1.5rem);
  position: relative;
  align-content: space-between;
}
.topGroup .todayCard {
  position: absolute;
  width: calc(600px + 1rem);
  height: 100%;
  z-index: 1;
  top: 0;
  left: 0;
  background: var(--anzhiyu-card-bg);
  border-radius: 12px;
  margin-left: 0.5rem;
  overflow: hidden;
  transition: 0.3s;
  display: flex;
  cursor: pointer;
  pointer-events: all;
}

.topGroup .todayCard .todayCard-info {
  position: absolute;
  bottom: 2rem;
  left: 2rem;
  z-index: 2;
  color: var(--anzhiyu-white);
  max-width: 60%;
  transition: 0.3s;
}

.topGroup .todayCard .todayCard-info .todayCard-tips {
  opacity: 0.8;
  font-size: 12px;
}

.topGroup .todayCard .todayCard-info .todayCard-title {
  font-size: 28px;
  font-weight: bold;
  line-height: 36px;
}
.topGroup .todayCard .todayCard-cover {
  position: absolute;
  min-width: 100%;
  min-height: 100%;
  top: 0;
  left: 0;
  background-size: cover;
  z-index: -1;
  transition: 0.3s;
}

.topGroup .banner-button-group {
  position: absolute;
  right: 2rem;
  bottom: 2rem;
  display: flex;
  transition: 0.3s;
}

.topGroup .todayCard.hide .todayCard-cover {
  transform: scale(1.2);
}

.topGroup .banner-button {
  background: var(--anzhiyu-white-op);
  border-radius: 20px;
  color: var(--anzhiyu-white);
  display: flex;
  align-items: center;
  z-index: 1;
  transition: 0.3s;
  cursor: pointer;
  backdrop-filter: saturate(180%) blur(20px);
  -webkit-backdrop-filter: blur(20px);
  transform: translateZ(0);
  height: 40px;
  width: 125px;
  justify-content: center;
}
.topGroup .banner-button-group .banner-button .banner-button-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.topGroup .banner-button i,
.topGroup .banner-button svg {
  margin-right: 8px;
  font-size: 22px;
}
.topGroup .todayCard::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  box-shadow: 0 -109px 133px -9px #000000 inset;
}

.topGroup .todayCard.hide {
  opacity: 0;
  pointer-events: none;
}
.topGroup .todayCard img {
  object-fit: cover;
  width: 100%;
  height: 80px;
  background: var(--anzhiyu-secondbg);
  border-radius: 12px 12px 0 0;
}
.topGroup .recent-post-item:nth-child(4),
.topGroup .recent-post-item:nth-child(5),
.topGroup .recent-post-item:nth-child(6) {
  margin-bottom: 0;
  margin-top: 4px;
}

.topGroup .recent-post-item {
  display: none;
  width: 200px;
  flex-direction: column;
  align-items: flex-start;
  background: var(--anzhiyu-card-bg);
  border-radius: 12px;
  overflow: hidden;
  min-width: 200px;
  height: 164px !important;
  max-height: 164px;
  border: var(--style-border-always);
  transition: 0.3s;
  position: relative;
  box-shadow: var(--anzhiyu-shadow-border);
  margin-left: 0.5rem;
  margin-right: 0px;
  margin-bottom: 0.5rem;
  cursor: pointer;
}
.topGroup .recent-post-item:hover .recent-post-info .article-title {
  color: var(--anzhiyu-main);
}
.topGroup .recent-post-item .post_cover {
  width: 100%;
}
.topGroup .recent-post-item .post_cover a {
  height: 100px;
  overflow: hidden;
  display: flex;
}
.topGroup span.recent-post-top-text {
  position: absolute;
  top: 0;
  left: -40px;
  display: flex;
  z-index: 1;
  background: var(--anzhiyu-theme);
  color: var(--anzhiyu-white);
  padding: 2px 8px;
  font-size: 12px;
  border-radius: 12px 0 12px 0;
  transition: 0.3s;
  cursor: pointer;
}

.topGroup .recent-post-item .post_cover img {
  object-fit: cover;
  width: 100%;
  background: var(--anzhiyu-secondbg);
  border-radius: 12px 12px 0 0;
}
.topGroup .recent-post-item .recent-post-info {
  padding: 0.3rem 0.5rem 0.3rem 0.5rem;
  transition: 0.3s;
}
.topGroup .recent-post-item .recent-post-info .article-title {
  -webkit-line-clamp: 2;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  line-height: 1.5;
  justify-content: center;
  align-items: flex-end;
  align-content: center;
  padding-top: 0.5rem;
  font-weight: bold;
  font-size: 1rem !important;
  padding: 0 !important;
}

.topGroup .recent-post-item:hover .recent-post-top-text {
  left: 0;
}

@media screen and (max-width: 1200px) {
  .topGroup .todayCard {
    background: #0e57d5;
  }

  div#bannerGroup {
    height: auto;
    width: auto;
  }

  .topGroup .recent-post-item {
    display: flex;
  }

  .categoryGroup .categoryItem:nth-child(3) {
    display: none;
  }
  .categoryGroup {
    flex-direction: column;
    height: 95%;
  }
  .topGroup {
    display: flex;
    flex-wrap: nowrap;
    width: auto;
    height: auto;
  }

  .topGroup .todayCard {
    display: none;
  }

  .swiper_container_card {
    display: flex;
    flex-direction: row !important;
    justify-content: flex-start !important;
    flex-wrap: nowrap;
    width: 100%;
    overflow-x: scroll;
  }

  .categoryItem {
    height: 48%;
    min-width: 200px;
    box-shadow: none !important;
  }
  .categoryGroup {
    display: flex !important;
  }
  #bbTimeList {
    margin: 0 1.5rem;
    max-width: 100%;
    width: auto;
  }
}

.swiper_container_card::-webkit-scrollbar {
  display: none;
}

.topGroup .banner-button:hover {
  background: var(--anzhiyu-theme);
  color: var(--anzhiyu-white);
}
