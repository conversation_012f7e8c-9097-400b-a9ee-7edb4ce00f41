name: Sync Blog to Server

on:
  push:
    branches: ["master"]
  workflow_dispatch:

env:
  SSH_HOST: ${{ secrets.SSH_HOST }}
  SSH_USER: ${{ secrets.SSH_USER }}
  SSH_PORT: ${{ secrets.SSH_PORT }}
  TARGET_DIR: ${{ secrets.TARGET_DIR }}

jobs:
  sync-to-server:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'anzhiyu-blog/package-lock.json'

      - name: Install dependencies and build
        run: |
          cd anzhiyu-blog
          echo "📦 Installing dependencies..."
          npm ci

          echo "🔌 Installing bangumi plugin..."
          npm install hexo-bilibili-bangumi --save

          echo "📡 Updating bangumi data..."
          npx hexo bangumi -u || echo "⚠️ Bangumi update failed, continuing with existing data"

          echo "🏗️ Building Hexo blog..."
          npm run clean
          npm run build

          echo "✅ Build completed successfully"

      - name: Verify build output
        run: |
          cd anzhiyu-blog
          if [ -d "public" ] && [ "$(ls -A public)" ]; then
            echo "✅ Build successful! Generated files:"
            ls -la public/ | head -10
          else
            echo "❌ Build failed: public directory is empty!"
            exit 1
          fi

      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/deploy_key
          chmod 600 ~/.ssh/deploy_key
          ssh-keyscan -p ${{ secrets.SSH_PORT }} ${{ secrets.SSH_HOST }} >> ~/.ssh/known_hosts

      - name: Sync project to server
        run: |
          echo "📁 Syncing project files to server..."

          # 彻底清理并重新创建目录
          ssh -i ~/.ssh/deploy_key -p ${{ secrets.SSH_PORT }} ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            # 创建备份
            if [ -d '${{ secrets.TARGET_DIR }}/anzhiyu-blog' ]; then
              sudo cp -r ${{ secrets.TARGET_DIR }}/anzhiyu-blog ${{ secrets.TARGET_DIR }}/anzhiyu-blog.backup.\$(date +%Y%m%d_%H%M%S)
              echo '✅ Backup created'
            fi

            # 彻底删除有问题的目录
            sudo rm -rf ${{ secrets.TARGET_DIR }}/anzhiyu-blog
            echo '✅ Old directory removed'

            # 重新创建目录并设置正确权限
            sudo mkdir -p ${{ secrets.TARGET_DIR }}/anzhiyu-blog
            sudo chown -R ${{ secrets.SSH_USER }}:${{ secrets.SSH_USER }} ${{ secrets.TARGET_DIR }}/
            sudo chmod -R 755 ${{ secrets.TARGET_DIR }}/

            echo '✅ Fresh directory created with correct permissions'
          "

          # 同步整个项目（排除不需要的文件，但保留web目录）
          rsync -avzr --delete \
            --exclude='.git*' \
            --exclude='anzhiyu-blog/node_modules/' \
            --exclude='anzhiyu-blog/.deploy_git/' \
            --exclude='*.log' \
            --exclude='.DS_Store' \
            --exclude='web/' \
            -e "ssh -i ~/.ssh/deploy_key -p ${{ secrets.SSH_PORT }}" \
            ./ ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:${{ secrets.TARGET_DIR }}/

      - name: Update nginx configuration
        run: |
          echo "🔧 Updating nginx configuration..."
          
          ssh -i ~/.ssh/deploy_key -p ${{ secrets.SSH_PORT }} ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            # 重新加载nginx配置以确保指向正确的目录
            docker exec nginx-proxy nginx -t && docker exec nginx-proxy nginx -s reload
            echo '✅ Nginx configuration reloaded'
          "

      - name: Cleanup
        if: always()
        run: |
          rm -f ~/.ssh/deploy_key

      - name: Deployment summary
        run: |
          echo "🎉 Sync completed successfully!"
          echo ""
          echo "📍 Blog URL: https://blog.xing2006.me"
          echo "📁 Server path: ${{ secrets.TARGET_DIR }}/anzhiyu-blog/"
          echo "🌐 Public files: ${{ secrets.TARGET_DIR }}/anzhiyu-blog/public/"
          echo ""
          echo "✅ All files synchronized to server"
