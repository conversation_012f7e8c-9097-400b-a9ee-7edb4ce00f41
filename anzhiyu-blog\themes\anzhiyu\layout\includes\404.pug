- let error_404_top_img = theme.error_404.background || theme.default_top_img
- let error_404_bg_img = `background-image: url('${url_for(error_404_top_img)}')`

#body-wrap.error
  include ./header/index.pug
  .error-box
    #error-wrap
      .error-content
        .error-img(style=error_404_bg_img)
        .error-info
          h1.error_title= '404'
          .error_subtitle= theme.error_404.subtitle
          a.button--animated(href=config.root)
            i.anzhiyufont.anzhiyu-icon-rocket
            = _p('回到主页')

    .aside-list
      .aside-list-group
          - let postLimit = theme.aside.card_recent_post.limit === 0 ? site.posts.length : theme.related_post.limit || 6
          - let sort = theme.aside.card_recent_post.sort === 'updated' ? 'updated' : 'date'
          - site.posts.sort(sort, -1).limit(postLimit).each(function(article){
            - let link = article.link || article.path
            - let title = article.title || _p('no_title')
            - let no_cover = article.cover === false || !theme.cover.aside_enable ? 'no-cover' : ''
            - let post_cover = article.cover
            .aside-list-item(class=no_cover)
              if post_cover && theme.cover.aside_enable
                a.thumbnail(href=url_for(link) title=title)
                  img(src=url_for(post_cover) onerror=`this.onerror=null;this.src='${url_for(theme.error_img.post_page)}'` alt=title)
              .content
                a.title(href=url_for(link) title=title)= title
                if theme.aside.card_recent_post.sort === 'updated'
                  time(datetime=date_xml(article.updated) title=_p('post.updated') + ' ' + full_date(article.updated)) #[=date(article.updated, config.date_format)]
                else
                  time(datetime=date_xml(article.date) title=_p('post.created') + ' ' + full_date(article.date)) #[=date(article.date, config.date_format)]
          - })
