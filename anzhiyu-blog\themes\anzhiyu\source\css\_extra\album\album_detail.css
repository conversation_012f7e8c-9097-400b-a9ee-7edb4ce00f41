/* 处理单页背景 */
body[data-type="album_detail"] #web_bg {
  background: var(--anzhiyu-background);
}
body[data-type="album_detail"] #page {
  border: 0;
  box-shadow: none !important;
  padding: 0 !important;
  background: 0 0 !important;
}
body[data-type="album_detail"] #page .page-title {
  display: none;
}

/* 处理页面样式 */
#album_detail .album-container-img {
  display: flex;
  align-items: center;
  justify-content: space-around;
  width: 100%;
  height: 100%;
  flex-wrap: wrap;
  margin-bottom: 0.3rem;
}
#album_detail .album-container-img .album-content-noimg {
  width: calc(100% / 2 - 5px);
}

#album_detail .album-content-img img {
  object-fit: cover;
  max-width: 100%;
  border-radius: 12px;
  margin: 10px 0;
}

#album_detail .album-content-img {
  height: 100%;
  max-height: 300px;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  position: relative;
}

#album_detail .album-content .datacont {
  order: 0;
  font-size: 0.8rem;
  font-weight: 700;
  color: var(--anzhiyu-fontcolor);
  width: 100%;
  line-height: 1.38;
  border-radius: 12px;
  margin-bottom: 0.5rem;
  display: flex;
  flex-direction: column;
  text-align: justify;
}
#album_detail p {
  margin: 0px;
}
#album_detail div.album-content {
  display: flex;
  flex-flow: wrap;
  border-radius: 12px;
  width: 100%;
  height: 100%;
}
#album_detail .timeline ul li.album-item {
  position: relative;
  width: 32%;
  border: var(--style-border-always);
  border-radius: 12px;
  padding: 1rem 1rem 0.5rem;
  transition: all 0.3s ease 0s;
  display: flex;
  flex-flow: column nowrap;
  justify-content: space-between;
  align-items: flex-start;
  background: var(--anzhiyu-card-bg);
  box-shadow: var(--anzhiyu-shadow-border);
  margin-right: 2%;
}
#album_detail .timeline #waterfall.show {
  opacity: 1;
}
#album_detail .timeline #waterfall {
  opacity: 0;
  transition: all 0.3s ease 0s;
}
#album_detail ul.list {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
}
#album_detail {
  margin-top: 1rem;
  width: 100%;
}
#album_detail > section > ul > li.album-item {
  margin-bottom: 1rem;
}

#album-tips {
  font-size: 14px;
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

#album_detail .timeline ul li.album-item hr {
  display: flex;
  position: relative;
  margin: 8px 0px;
  border: 1px dashed var(--anzhiyu-theme-op);
  width: 100%;
}

#album_detail .album-info {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  width: 95%;
}

#album_detail > section > ul > li > div .album-info-time,
#album_detail > section > ul > li > div .album-info-address,
#album_detail > section > ul > li > div .album-info-from {
  color: var(--anzhiyu-fontcolor);
  font-size: 0.7rem;
  background-color: var(--anzhiyu-gray-op);
  padding: 0px 8px;
  border-radius: 20px;
  cursor: default;
  display: flex;
  align-items: center;
  height: 35px;
  line-height: 35px;
  margin: 4px;
  overflow: hidden;
}
#album_detail > section > ul > li > div .album-info-time time {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

#album_detail > section > ul > li > div .album-info-address,
#album_detail > section > ul > li > div .album-info-from {
  margin-left: 4px;
}
#album_detail > section > ul > li > div .album-info-address span,
#album_detail > section > ul > li > div .album-info-from span {
  margin-left: 4px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
#album_detail .album-info .anzhiyufont.anzhiyu-icon-instagram {
  margin-right: 8px;
}

#album_detail .album-bottom {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 10px;
}

#album_detail .album-bottom .album-reply {
  cursor: pointer;
  width: 5%;
}

#album_detail .timeline ul li.album-item:hover {
  border: var(--style-border-hover);
}

#album_detail .album-content-link {
  display: flex;
  margin-left: 0.5rem;
  font-size: 0.7rem;
  align-items: center;
  background-color: rgba(245, 108, 108, 0.13);
  color: rgb(245, 108, 108);
  padding: 0px 8px;
  border-radius: 20px;
}
#album_detail .album-content-link i {
  margin-right: 3px;
}
#album_detail .album-content-link:hover {
  background-color: var(--anzhiyu-main);
  color: var(--anzhiyu-white);
}
#album_detail .album-music {
  width: 100%;
  height: 90px;
  margin: 0.5rem 0;
  border-radius: 8px;
  overflow: hidden;
  border: var(--style-border-always);
  background: var(--anzhiyu-secondbg);
}
#album_detail .aplayer {
  margin: 0;
}

#album_detail .aplayer.aplayer-withlrc .aplayer-pic {
  height: 82px;
  width: 82px;
  margin: 4px;
  border-radius: 4px;
}
.album-music .aplayer.aplayer-withlrc .aplayer-info {
  padding: 5px 7px 0;
}
#album_detail .aplayer .aplayer-info .aplayer-music {
  height: 23px;
}
#album_detail .aplayer .aplayer-info .aplayer-music .aplayer-title {
  font-size: 0.8rem;
  font-weight: 700;
  margin: 0;
  color: var(--anzhiyu-fontcolor);
}

#album_detail .aplayer .aplayer-info .aplayer-controller {
  align-items: center;
}
#album_detail .aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap {
  padding: 0;
}
#album_detail .aplayer .aplayer-info .aplayer-controller .aplayer-time {
  position: initial;
}
#album_detail .aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar {
  background: var(--anzhiyu-gray);
  height: 8px;
  border-radius: 12px;
  transition: 0.3s;
  overflow: hidden;
}
#album_detail .aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-loaded {
  height: 100%;
  border-radius: 12px;
}
#album_detail .aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-played {
  height: 100%;
  border-radius: 12px;
}
#album_detail .aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-played .aplayer-thumb {
  display: none;
}
#album_detail .aplayer .aplayer-info .aplayer-controller .aplayer-time {
  position: initial;
}

body[data-type="album_detail"] #page .author-content-item .card-content .banner-button-group .banner-button:hover {
  color: var(--anzhiyu-white);
  border-radius: 20px !important;
}

/* 响应式 */
@media screen and (max-width: 1200px) {
  #album_detail .timeline ul li.album-item {
    width: 49%;
    margin-right: 1%;
  }
}
@media screen and (max-width: 768px) {
  #album_detail .timeline ul li.album-item {
    width: 100%;
    margin-right: 0px;
  }
}
[data-theme="dark"] #album_detail .album-music .aplayer,
[data-theme="dark"] #album_detail .aplayer .aplayer-lrc:before,
[data-theme="dark"] #album_detail .aplayer .aplayer-lrc:after {
  background: var(--anzhiyu-card-bg);
  color: var(--anzhiyu-fontcolor);
}
#album_detail .aplayer .aplayer-lrc p {
  color: var(--anzhiyu-fontcolor);
}

#album_detail .type-gallery {
  overflow: hidden;
  margin-top: 40px;
}

#album_detail_gallery_load_more {
  opacity: 0;
}

#album_detail .tag-address {
  display: flex;
  position: absolute;
  left: 8px;
  top: 8px;
  padding: 4px 6px;
  border-radius: 8px;
  background: var(--anzhiyu-black-op);
  font-size: 12px;
  color: var(--anzhiyu-white);
  transition: 0.3s;
  z-index: 1;
  user-select: none;
}
