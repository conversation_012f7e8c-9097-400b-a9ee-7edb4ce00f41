if theme.aside.card_archives.enable
  .card-archives
    - let type = theme.aside.card_archives.type || 'monthly'
    - let format = theme.aside.card_archives.format || 'MMMM YYYY'
    - let order = theme.aside.card_archives.order || -1
    - let limit = theme.aside.card_archives.limit === 0 ? 0 : theme.aside.card_archives.limit || 8
    != aside_archives({ type:type, format: format, order: order, limit: limit })
  hr
