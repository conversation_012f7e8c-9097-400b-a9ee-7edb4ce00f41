div#runtime {
  width: 180px;
  margin: auto;
  color: #fff;
  padding-inline: 5px;
  border-radius: 10px;
  background-color: rgba(0, 0, 0, 0.7);
}
#workboard {
  font-size: 12px;
}
[data-theme="dark"] div#runtime {
  color: #28b4c8;
  box-shadow: 0 0 5px rgba(28, 69, 218, 0.71);
  animation: flashlight 1s linear infinite alternate;
}
#ghbdages .github-badge img {
  height: 20px;
  max-width: 150px;
  border-radius: 4px;
  pointer-events: none;
}
#workboard img.boardsign {
  width: 100%;
  max-width: 130px;
  border-radius: 0px;
}
@-moz-keyframes flashlight {
  from {
    box-shadow: 0 0 5px #1478d2;
  }
  to {
    box-shadow: 0 0 2px #1478d2;
  }
}
@-webkit-keyframes flashlight {
  from {
    box-shadow: 0 0 5px #1478d2;
  }
  to {
    box-shadow: 0 0 2px #1478d2;
  }
}
@-o-keyframes flashlight {
  from {
    box-shadow: 0 0 5px #1478d2;
  }
  to {
    box-shadow: 0 0 2px #1478d2;
  }
}
@keyframes flashlight {
  from {
    box-shadow: 0 0 5px #1478d2;
  }
  to {
    box-shadow: 0 0 2px #1478d2;
  }
}
