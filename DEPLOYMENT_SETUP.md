# 博客自动部署配置指南

## 概述

这个项目使用GitHub Actions自动将博客同步到服务器。当您推送代码到master分支时，工作流会自动：

1. 构建Hexo博客
2. 将整个项目同步到服务器
3. 重新加载nginx配置

## 必需的GitHub Secrets

在GitHub仓库的Settings > Secrets and variables > Actions中添加以下secrets：

### SSH连接配置
- `SSH_HOST`: 服务器IP地址或域名 (例如: `************`)
- `SSH_USER`: SSH用户名 (例如: `root`)
- `SSH_PORT`: SSH端口 (例如: `22`)
- `SSH_PRIVATE_KEY`: SSH私钥内容

### 服务器路径配置
- `TARGET_DIR`: 服务器上的目标目录 (例如: `/var/www/blog`)

## SSH私钥配置

### 1. 生成SSH密钥对（如果还没有）
```bash
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
```

### 2. 将公钥添加到服务器
```bash
# 在服务器上执行
cat ~/.ssh/id_rsa.pub >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys
```

### 3. 将私钥添加到GitHub Secrets
- 复制私钥内容：`cat ~/.ssh/id_rsa`
- 在GitHub仓库中添加为`SSH_PRIVATE_KEY` secret

## 服务器配置要求

### 1. 确保服务器已安装必要软件
- Docker (用于nginx-proxy容器)
- rsync (用于文件同步)

### 2. 确保nginx-proxy容器正在运行
```bash
docker ps | grep nginx-proxy
```

### 3. 确保目标目录存在且有正确权限
```bash
mkdir -p /var/www/blog
chown -R $SSH_USER:$SSH_USER /var/www/blog
```

## 工作流触发方式

### 自动触发
- 推送代码到master分支时自动运行

### 手动触发
- 在GitHub仓库的Actions页面手动运行工作流

## 部署后验证

1. 检查博客是否可以访问：https://blog.xing2006.me
2. 检查服务器上的文件是否已更新：
   ```bash
   ls -la /var/www/blog/anzhiyu-blog/public/
   ```

## 故障排除

### 常见问题

1. **SSH连接失败**
   - 检查SSH_HOST、SSH_USER、SSH_PORT是否正确
   - 确认SSH私钥格式正确
   - 检查服务器防火墙设置

2. **文件同步失败**
   - 确认TARGET_DIR路径存在
   - 检查目录权限
   - 确认rsync已安装

3. **nginx重载失败**
   - 确认nginx-proxy容器正在运行
   - 检查nginx配置文件语法

### 查看工作流日志
在GitHub仓库的Actions页面查看详细的执行日志。

## 安全注意事项

1. 定期轮换SSH密钥
2. 使用最小权限原则配置SSH用户
3. 定期检查GitHub Secrets的使用情况
4. 考虑使用专用的部署用户而不是root用户

## 自定义配置

如需修改同步行为，请编辑`.github/workflows/sync-to-server.yml`文件。

主要可配置项：
- 排除的文件和目录
- Node.js版本
- 构建命令
- 同步选项
