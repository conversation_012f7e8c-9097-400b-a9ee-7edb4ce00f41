if theme.menu
  //- for mobile sidebar
  - let sidebarChildHide = theme.hide_sidebar_menu_child ? 'hide' : ''

  .menus_items
    each value, label in theme.menu
      if typeof value !== 'object'
        .menus_item
          a.site-page.faa-parent.animated-hover(href=url_for(trim(value.split('||')[0])))
            if value.split('||')[1]
              - var icon_value = trim(value.split('||')[1])
              - var anima_value = value.split('||')[2] ? trim(value.split('||')[2]) : 'faa-tada'
              if icon_value.substring(0,2)=="fa"      
                i(class=icon_value + ' ' + anima_value)
              else if icon_value.substring(0,4)=="icon"
                svg.icon(aria-hidden="true" class=anima_value)
                  use(xlink:href=`#`+ icon_value)
              else if icon_value.substring(0,4)=="anzhiyu"
                i.anzhiyufont(style="font-size: 0.9em;" class=icon_value + ' ' + anima_value)
            span=' '+label
      else
        .menus_item
          a.site-page(href='javascript:void(0);')
            if label.split('||')[1]
              - var icon_label = trim(label.split('||')[1])
              - var anima_label = label.split('||')[2] ? trim(label.split('||')[2]) : 'faa-tada'
              if icon_label.substring(0,2)=="fa"      
                i(class=icon_label + ' ' + anima_label)
              else if (icon_label.startsWith("icon"))
                svg.icon(aria-hidden="true" class=anima_label)
                  use(xlink:href=`#`+ icon_label)
              else if (icon_label.startsWith("anzhiyu"))
                i.anzhiyufont(style="font-size: 0.9em;" class=icon_label + ' ' + anima_label)
            span=' '+ trim(label.split('||')[0])
          ul.menus_item_child
            each val,lab in value
              li
                a.site-page.child.faa-parent.animated-hover(href=url_for(trim(val.split('||')[0])))
                  if val.split('||')[1]
                    - var icon_val = trim(val.split('||')[1])
                    - var anima_val = val.split('||')[2] ? trim(val.split('||')[2]) : 'faa-tada'
                    if icon_val.substring(0,2)=="fa"      
                      i(class=icon_val + ' ' + anima_val)
                    else if (icon_val.startsWith("icon"))
                      svg.icon(aria-hidden="true" class=anima_val)
                        use(xlink:href=`#`+ icon_val)
                    else if (icon_val.startsWith("anzhiyu"))
                      i.anzhiyufont(style="font-size: 0.9em;" class=icon_val + ' ' + anima_val)
                  span=' '+ lab