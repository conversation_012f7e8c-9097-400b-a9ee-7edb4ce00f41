#greetingBox {
  position: fixed;
  top: 10px;
  left: 15%;
  width: 400px;
  text-align: center;
  z-index: 999;
  pointer-events: none;
}
@media screen and (max-width: 768px) {
  #greetingBox {
    display: none;
  }
}

#greeting {
  display: inline-block;
  position: relative;
  opacity: 0;
  top: -110px;
  padding: 5px 40px;
  border-radius: 50px;
  background-color: white;
  color: black;
  font-size: small;
  transition: 0.5s;
  box-shadow: rgb(0 0 0 / 5%) 0 10px 20px;
}

#greeting.shown {
  opacity: 1;
  top: 0;
}
