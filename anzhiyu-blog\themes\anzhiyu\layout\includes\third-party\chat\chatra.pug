//- https://chatra.io/help/api/
script.
  (() => {
    const isChatBtn = !{theme.chat_btn}
    const isChatHideShow = !{theme.chat_hide_show}

    if (isChatBtn) {
      const close = () => {
        Chatra('minimizeWidget')
        Chatra('hide')
      }

      const open = () => {
        Chatra('openChat', true)
        Chatra('show')
      }

      window.ChatraSetup = {
        startHidden: true
      }
    
      window.chatBtnFn = () => {
        const isShow = document.getElementById('chatra').classList.contains('chatra--expanded')
        isShow ? close() : open()
      }
    } else if (isChatHideShow) {
      window.chatBtn = {
        hide: () => {
          Chatra('hide')
        },
        show: () => {
          Chatra('show')
        }
      }
    }

    (function(d, w, c) {
      w.ChatraID = '#{theme.chatra.id}'
      var s = d.createElement('script')
      w[c] = w[c] || function() {
          (w[c].q = w[c].q || []).push(arguments)
      }
      s.async = true
      s.src = 'https://call.chatra.io/chatra.js'
      if (d.head) d.head.appendChild(s)
    })(document, window, 'Chatra')

  })()


