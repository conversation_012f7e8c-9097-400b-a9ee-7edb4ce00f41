# Hexo 博客自动化部署与更新教程

## 📋 目录
- [项目概述](#项目概述)
- [初始部署配置](#初始部署配置)
- [日常更新流程](#日常更新流程)
- [常见操作](#常见操作)
- [故障排除](#故障排除)
- [高级配置](#高级配置)

## 🎯 项目概述

本项目实现了基于 GitHub Actions 的 Hexo 博客自动化部署系统，支持：
- ✅ 自动构建和部署
- ✅ 多包管理器支持（npm/yarn/pnpm）
- ✅ 服务器文件同步
- ✅ SSL 证书自动续期
- ✅ 多域名访问支持

### 架构说明
```
GitHub 仓库 → GitHub Actions → 服务器部署
     ↓              ↓              ↓
   源代码         自动构建        静态文件
```

## 🚀 初始部署配置

### 1. GitHub Secrets 配置

在 GitHub 仓库的 Settings → Secrets and variables → Actions 中添加以下密钥：

| 密钥名称 | 说明 | 示例值 |
|---------|------|--------|
| `SSH_HOST` | 服务器IP地址 | `************` |
| `SSH_USER` | SSH用户名 | `deploy` |
| `SSH_PORT` | SSH端口 | `22` |
| `TARGET_DIR` | 目标目录 | `/var/www/blog` |
| `SSH_PRIVATE_KEY` | SSH私钥 | `-----BEGIN OPENSSH PRIVATE KEY-----...` |

### 2. 服务器环境配置

#### 创建部署用户
```bash
# 创建部署用户
sudo useradd -m -s /bin/bash deploy
sudo mkdir -p /home/<USER>/.ssh
sudo chown deploy:deploy /home/<USER>/.ssh
sudo chmod 700 /home/<USER>/.ssh

# 设置SSH密钥
sudo nano /home/<USER>/.ssh/authorized_keys
# 粘贴公钥内容
sudo chown deploy:deploy /home/<USER>/.ssh/authorized_keys
sudo chmod 600 /home/<USER>/.ssh/authorized_keys
```

#### 创建网站目录
```bash
# 创建网站目录
sudo mkdir -p /var/www/blog
sudo chown deploy:deploy /var/www/blog
sudo chmod 755 /var/www/blog
```

### 3. Nginx 配置

#### 静态博客配置 (`/opt/nginx-proxy/conf.d/myblog.xing2006.me.conf`)
```nginx
server {
    listen 80;
    server_name myblog.xing2006.me ************ _ default_server;

    # 网站根目录
    root /var/www/blog;
    index index.html index.htm;

    # 静态文件处理
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 日志
    access_log /var/log/nginx/myblog.xing2006.me.access.log;
    error_log /var/log/nginx/myblog.xing2006.me.error.log;
}
```

#### HTTPS 重定向配置 (`/opt/nginx-proxy/conf.d/blog.xing2006.me.conf`)
```nginx
server {
    listen 80;
    server_name blog.xing2006.me;

    # Let's Encrypt 验证路径
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # 重定向到 HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}

server {
    listen 443 ssl http2;
    server_name blog.xing2006.me;

    # SSL 配置
    ssl_certificate /etc/nginx/ssl/live/blog.xing2006.me/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/live/blog.xing2006.me/privkey.pem;

    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头部
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # 静态文件服务
    location / {
        root /var/www/blog;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }
}
```

## 📝 日常更新流程

### 简化版流程（推荐）
```bash
# 1. 本地修改博客内容
cd anzhiyu-blog
# 编辑文章或配置

# 2. 提交并推送
git add .
git commit -m "更新描述"
git push origin main

# 3. 自动部署完成 ✨
# GitHub Actions 会自动构建并部署到服务器
```

### 详细版流程

#### 1. 本地开发环境
```bash
# 安装依赖（首次）
cd anzhiyu-blog
npm install  # 或 yarn install / pnpm install

# 本地预览
npm run server  # 或 hexo server
# 访问 http://localhost:4000 预览
```

#### 2. 创建新文章
```bash
# 创建新文章
hexo new post "文章标题"
# 编辑 source/_posts/文章标题.md

# 创建新页面
hexo new page "页面名称"
```

#### 3. 本地构建测试
```bash
# 清理缓存
npm run clean  # 或 hexo clean

# 生成静态文件
npm run build  # 或 hexo generate

# 本地预览构建结果
npm run server  # 或 hexo server
```

#### 4. 提交到 GitHub
```bash
# 检查文件状态
git status

# 添加文件
git add .

# 提交更改
git commit -m "feat: 添加新文章《文章标题》"

# 推送到远程仓库
git push origin main
```

#### 5. 监控部署状态
1. 访问 GitHub 仓库的 **Actions** 页面
2. 查看最新的 workflow 运行状态
3. 如果失败，点击查看详细日志

## 🛠️ 常见操作

### 发布新文章
```bash
# 创建文章
hexo new post "我的新文章"

# 编辑文章
# source/_posts/我的新文章.md

# 提交发布
git add . && git commit -m "发布：我的新文章" && git push
```

### 修改博客配置
```bash
# 编辑主配置文件
nano _config.yml

# 或编辑主题配置
nano _config.anzhiyu.yml

# 提交更改
git add . && git commit -m "更新博客配置" && git push
```

### 更新主题
```bash
# 如果主题是 git submodule
git submodule update --remote

# 或直接更新主题文件
# 然后提交
git add . && git commit -m "更新主题" && git push
```

### 手动触发部署
1. 访问 GitHub 仓库
2. 点击 **Actions** 标签
3. 选择 "Build & Deploy Hexo Blog" workflow
4. 点击 **Run workflow** 按钮

## 🔧 故障排除

### 常见问题

#### 1. 部署失败：SSH 连接问题
```bash
# 检查 SSH 密钥格式
# 确保私钥是 OpenSSH 格式，不是 PuTTY 格式

# 测试 SSH 连接
ssh -i ~/.ssh/id_rsa deploy@your-server-ip
```

#### 2. 构建失败：依赖问题
```bash
# 删除 node_modules 和 lock 文件
rm -rf node_modules package-lock.json

# 重新安装
npm install

# 提交新的 lock 文件
git add package-lock.json && git commit -m "更新依赖" && git push
```

#### 3. 网站无法访问
```bash
# 检查 Nginx 配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx

# 检查文件权限
ls -la /var/www/blog/
```

### 日志查看

#### GitHub Actions 日志
1. GitHub 仓库 → Actions
2. 点击失败的 workflow
3. 查看详细错误信息

#### 服务器日志
```bash
# Nginx 访问日志
tail -f /var/log/nginx/myblog.xing2006.me.access.log

# Nginx 错误日志
tail -f /var/log/nginx/myblog.xing2006.me.error.log

# 系统日志
journalctl -u nginx -f
```

## ⚙️ 高级配置

### 自定义构建脚本
可以在 `package.json` 中添加自定义脚本：
```json
{
  "scripts": {
    "build": "hexo clean && hexo generate",
    "deploy": "hexo clean && hexo generate && hexo deploy",
    "dev": "hexo server --draft",
    "clean": "hexo clean"
  }
}
```

### 环境变量配置
在 GitHub Actions 中可以设置环境变量：
```yaml
env:
  NODE_ENV: production
  HEXO_ALGOLIA_INDEXING_KEY: ${{ secrets.ALGOLIA_KEY }}
```

### 多环境部署
可以配置不同分支部署到不同环境：
```yaml
on:
  push:
    branches: 
      - main      # 生产环境
      - develop   # 测试环境
```

## 🌐 访问地址

部署完成后，博客可通过以下地址访问：
- **HTTP**: http://myblog.xing2006.me
- **HTTPS**: https://blog.xing2006.me
- **IP直接访问**: http://************

## 📞 技术支持

如遇到问题，请检查：
1. GitHub Actions 运行日志
2. 服务器 Nginx 日志
3. SSH 连接和权限设置
4. 域名 DNS 解析状态

## 📚 附录

### A. GitHub Actions 配置文件详解

完整的 `.github/workflows/deploy.yml` 配置：

```yaml
name: Build & Deploy Hexo Blog

on:
  push:
    branches: ["main", "master"]
  workflow_dispatch:  # 允许手动触发

env:
  SSH_HOST: ${{ secrets.SSH_HOST }}
  SSH_USER: ${{ secrets.SSH_USER }}
  SSH_PORT: ${{ secrets.SSH_PORT }}
  TARGET_DIR: ${{ secrets.TARGET_DIR }}

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: false

      # 检测包管理器
      - name: Detect package manager
        id: pm
        run: |
          cd anzhiyu-blog
          if [ -f pnpm-lock.yaml ]; then echo "pm=pnpm" >> $GITHUB_OUTPUT
          elif [ -f yarn.lock ]; then echo "pm=yarn" >> $GITHUB_OUTPUT
          elif [ -f package-lock.json ]; then echo "pm=npm" >> $GITHUB_OUTPUT
          else echo "pm=npm" >> $GITHUB_OUTPUT; fi

      # 设置 Node.js 环境
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: ${{ steps.pm.outputs.pm }}
          cache-dependency-path: 'anzhiyu-blog/package-lock.json'

      # 安装依赖和构建
      - name: Install dependencies and build
        run: |
          set -e
          cd anzhiyu-blog

          # 安装依赖
          if [ "${{ steps.pm.outputs.pm }}" = "pnpm" ]; then
            npm install -g pnpm
            pnpm install --frozen-lockfile
          elif [ "${{ steps.pm.outputs.pm }}" = "yarn" ]; then
            yarn install --frozen-lockfile
          else
            npm ci
          fi

          # 构建 Hexo 博客
          if [ "${{ steps.pm.outputs.pm }}" = "pnpm" ]; then
            pnpm run clean && pnpm run build
          elif [ "${{ steps.pm.outputs.pm }}" = "yarn" ]; then
            yarn clean && yarn build
          else
            npm run clean && npm run build
          fi

          # 检查构建输出
          if [ -d "public" ]; then
            echo "OUT_DIR=anzhiyu-blog/public" >> $GITHUB_ENV
          else
            echo "Build failed: public directory not found!"
            exit 1
          fi

      # 部署到服务器
      - name: Deploy to server
        uses: burnett01/rsync-deployments@6.0.0
        with:
          switches: -avzr --delete --exclude='.git*' --exclude='node_modules'
          path: ${{ env.OUT_DIR }}/
          remote_path: ${{ secrets.TARGET_DIR }}
          remote_host: ${{ secrets.SSH_HOST }}
          remote_port: ${{ secrets.SSH_PORT }}
          remote_user: ${{ secrets.SSH_USER }}
          remote_key: ${{ secrets.SSH_PRIVATE_KEY }}
```

### B. 常用 Git 命令速查

```bash
# 基础操作
git status                    # 查看状态
git add .                     # 添加所有文件
git add filename             # 添加指定文件
git commit -m "message"      # 提交更改
git push origin main         # 推送到远程

# 分支操作
git branch                   # 查看分支
git checkout -b new-branch   # 创建并切换分支
git merge branch-name        # 合并分支

# 撤销操作
git reset HEAD~1             # 撤销最后一次提交
git checkout -- filename    # 撤销文件修改
git stash                    # 暂存更改
git stash pop               # 恢复暂存的更改

# 查看历史
git log --oneline           # 查看提交历史
git diff                    # 查看差异
```

### C. Hexo 常用命令

```bash
# 基础命令
hexo init [folder]          # 初始化博客
hexo new [layout] <title>   # 创建新文章
hexo generate              # 生成静态文件
hexo server                # 启动本地服务器
hexo deploy                # 部署博客
hexo clean                 # 清理缓存

# 简写形式
hexo g                     # 等同于 generate
hexo s                     # 等同于 server
hexo d                     # 等同于 deploy

# 组合命令
hexo clean && hexo g       # 清理并生成
hexo g -d                  # 生成并部署
hexo s --draft             # 预览草稿
```

### D. 服务器维护命令

```bash
# Nginx 操作
sudo nginx -t                    # 测试配置
sudo systemctl reload nginx     # 重载配置
sudo systemctl restart nginx    # 重启服务
sudo systemctl status nginx     # 查看状态

# Docker 操作
docker ps                       # 查看运行容器
docker logs nginx-proxy         # 查看日志
docker restart nginx-proxy      # 重启容器

# 文件权限
sudo chown -R deploy:deploy /var/www/blog
sudo chmod -R 755 /var/www/blog

# 磁盘空间
df -h                          # 查看磁盘使用
du -sh /var/www/blog          # 查看目录大小
```

### E. 性能优化建议

#### 1. 图片优化
```bash
# 安装图片压缩插件
npm install hexo-imagemin --save

# 配置 _config.yml
imagemin:
  enable: true
  interlaced: false
  multipass: false
  optimizationLevel: 2
  pngquant: false
  progressive: false
```

#### 2. 缓存配置
```nginx
# 在 Nginx 配置中添加
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Vary Accept-Encoding;
    gzip_static on;
}
```

#### 3. CDN 配置
```yaml
# _config.yml 中配置 CDN
url: https://blog.xing2006.me
root: /
permalink: :year/:month/:day/:title/

# 使用 CDN 加速静态资源
cdn:
  enable: true
  host: https://cdn.example.com
```

## 🎯 最佳实践

### 1. 提交信息规范
```bash
# 使用语义化提交信息
feat: 添加新功能
fix: 修复bug
docs: 更新文档
style: 样式调整
refactor: 代码重构
test: 添加测试
chore: 构建过程或辅助工具的变动

# 示例
git commit -m "feat: 添加文章《Docker 入门教程》"
git commit -m "fix: 修复导航栏在移动端的显示问题"
git commit -m "docs: 更新 README 文档"
```

### 2. 分支管理策略
```bash
# 主分支：main/master（生产环境）
# 开发分支：develop（开发环境）
# 功能分支：feature/功能名称
# 修复分支：hotfix/问题描述

# 创建功能分支
git checkout -b feature/new-article
# 开发完成后合并到 develop
git checkout develop
git merge feature/new-article
# 测试通过后合并到 main
git checkout main
git merge develop
```

### 3. 备份策略
```bash
# 定期备份重要文件
# 1. 源代码（已通过 Git 管理）
# 2. 配置文件
# 3. 静态资源
# 4. 数据库（如果有）

# 自动备份脚本示例
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf /backup/blog_backup_$DATE.tar.gz /var/www/blog
find /backup -name "blog_backup_*.tar.gz" -mtime +7 -delete
```

---

## 📋 检查清单

### 初始部署检查
- [ ] GitHub Secrets 配置完成
- [ ] SSH 密钥配置正确
- [ ] 服务器用户和目录权限设置
- [ ] Nginx 配置文件正确
- [ ] SSL 证书配置（如需要）
- [ ] 域名 DNS 解析正确
- [ ] 首次部署成功

### 日常更新检查
- [ ] 本地预览正常
- [ ] 提交信息清晰
- [ ] GitHub Actions 运行成功
- [ ] 网站访问正常
- [ ] 新内容显示正确

### 定期维护检查
- [ ] 服务器磁盘空间充足
- [ ] SSL 证书未过期
- [ ] 依赖包安全更新
- [ ] 备份文件完整
- [ ] 性能监控正常

---

*最后更新：2025年8月23日*

> 💡 **提示**: 这个教程涵盖了从初始部署到日常维护的完整流程。建议收藏此文档，在遇到问题时可以快速查阅相关章节。
