extends includes/layout.pug

block content
  main#main
    .bangumi-page
      .bangumi-header
        h1.bangumi-title 追番列表
        p.bangumi-subtitle 生命不息，追番不止！
      
      .bangumi-tabs
        .bangumi-tab.bangumi-active(data-type="wantWatch") 想看
        .bangumi-tab(data-type="watching") 在看  
        .bangumi-tab(data-type="watched") 已看
      
      .bangumi-content
        #bangumi-item1.bangumi-list.bangumi-show
        #bangumi-item2.bangumi-list.bangumi-hide
        #bangumi-item3.bangumi-list.bangumi-hide
        
        .bangumi-pagination
          .bangumi-button.bangumi-prev 上一页
          .bangumi-pagenum 1 / 1
          .bangumi-button.bangumi-next 下一页

  script.
    const bangumiLazyload = false;
    "use strict";
    
    // 追番数据
    const bangumiData = !{JSON.stringify(site.data.bangumis || {})};
    
    // 初始化追番页面
    function initBangumi() {
      if (!bangumiData || !bangumiData.wantWatch) {
        console.log('追番数据未找到');
        return;
      }
      
      // 渲染各个分类的数据
      renderBangumiList('wantWatch', bangumiData.wantWatch, 1);
      renderBangumiList('watching', bangumiData.watching, 2);  
      renderBangumiList('watched', bangumiData.watched, 3);
      
      // 绑定标签切换事件
      bindTabEvents();
      
      // 绑定分页事件
      bindPaginationEvents();
    }
    
    // 渲染追番列表
    function renderBangumiList(type, data, containerId) {
      if (!data || !Array.isArray(data)) return;
      
      const container = document.getElementById(`bangumi-item${containerId}`);
      if (!container) return;
      
      let html = '';
      data.forEach(item => {
        html += `
          <div class="bangumi-item">
            <div class="bangumi-picture">
              <img src="${item.cover}" alt="${item.title}" loading="lazy">
            </div>
            <div class="bangumi-info">
              <div class="bangumi-title">
                <a href="https://www.bilibili.com/bangumi/play/ss${item.id}" target="_blank">${item.title}</a>
              </div>
              <div class="bangumi-info-items">
                <div class="bangumi-info-item bangumi-type">
                  <span class="bangumi-info-label">类型</span>
                  <em>${item.type || '番剧'}</em>
                </div>
                <div class="bangumi-info-item bangumi-area">
                  <span class="bangumi-info-label">地区</span>
                  <em>${item.area || '日本'}</em>
                </div>
                <div class="bangumi-info-item bangumi-play">
                  <span class="bangumi-info-label">总播放</span>
                  <em>${item.view || '0'}</em>
                </div>
                <div class="bangumi-info-item bangumi-follow">
                  <span class="bangumi-info-label">追番人数</span>
                  <em>${item.follow || '0'}</em>
                </div>
                <div class="bangumi-info-item bangumi-coin">
                  <span class="bangumi-info-label">硬币数</span>
                  <em>${item.coin || '0'}</em>
                </div>
                <div class="bangumi-info-item bangumi-danmaku">
                  <span class="bangumi-info-label">弹幕总数</span>
                  <em>${item.danmaku || '0'}</em>
                </div>
                <div class="bangumi-info-item bangumi-info-item-score">
                  <span class="bangumi-info-label">评分</span>
                  <em>${item.score || '暂无'}</em>
                </div>
              </div>
              <div class="bangumi-comments">
                <p><strong>简介：</strong>${item.des || '暂无简介'}</p>
              </div>
            </div>
          </div>
        `;
      });
      
      container.innerHTML = html;
    }
    
    // 绑定标签切换事件
    function bindTabEvents() {
      const tabs = document.querySelectorAll('.bangumi-tab');
      const lists = document.querySelectorAll('.bangumi-list');
      
      tabs.forEach((tab, index) => {
        tab.addEventListener('click', function() {
          // 移除所有活跃状态
          tabs.forEach(t => t.classList.remove('bangumi-active'));
          lists.forEach(l => {
            l.classList.remove('bangumi-show');
            l.classList.add('bangumi-hide');
          });
          
          // 添加当前活跃状态
          this.classList.add('bangumi-active');
          lists[index].classList.remove('bangumi-hide');
          lists[index].classList.add('bangumi-show');
        });
      });
    }
    
    // 绑定分页事件（暂时简单实现）
    function bindPaginationEvents() {
      // 这里可以根据需要实现分页功能
    }
    
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', initBangumi);
