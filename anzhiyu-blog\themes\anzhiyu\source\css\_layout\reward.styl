.post-reward
  position: relative;
  text-align: center;
  margin-top: 0rem;
  display: flex;
  justify-content: center;
  &:hover
    .reward-button
      color: var(--anzhiyu-white);
      background: var(--anzhiyu-theme);
      box-shadow: none;
      
    .reward-main
      display: flex !important;
      justify-content: left;


  & > *
    pointer-events: auto

  .reward-button 
    border-radius: 8px
    background: var(--anzhiyu-red);
    color: var(--anzhiyu-white);
    padding: 0;
    margin-right: 0.5rem;
    width: 126px;
    height: 40px;
    line-height: 39px;
    box-shadow: var(--anzhiyu-shadow-red);
    display: inline-block;
    cursor: pointer;
    transition: all 0.4s ease 0s;

  .reward-main
    position: absolute;
    bottom: 40px;
    if hexo-config('ptool.enable') == true && hexo-config('ptool.mode')
      left: 0;
    else
      left: -70%;
    z-index: 90;
    display: none;
    padding: 0px 0px 15px;
    width: 100%;
    .reward-all
      border-radius: 12px;
      background: var(--anzhiyu-background);
      border: var(--style-border-always);
      padding: 0.8rem;
      display: flex;
      margin: 0px;
      box-shadow: var(--anzhiyu-shadow-border);
      flex-direction: column;
      align-items: center;
      &::before 
        position: absolute;
        bottom: -10px;
        left: 0px;
        width: 100%;
        height: 20px;
        content: "";

      .reward-title
        font-weight: bold;
        color: var(--anzhiyu-red);
      .reward-group
        display: flex;
        margin-top: 0.5rem;
        padding: 0;
        .reward-item
          display: inline-block;
          padding: 0px 8px;
          list-style-type: none;
          vertical-align: top;
          .post-qr-code-img
            box-shadow: var(--anzhiyu-shadow-lightblack);
            border-radius: 12px;
            border: var(--style-border-always);
          .post-qr-code-desc
              padding-top: 0rem;
              margin-top: -8px;
              margin-bottom: 8px;
          img 
            width: 130px;
            height: 130px;
            max-width: fit-content
          &:first-child img
            border-color: var(--anzhiyu-green);
          &:last-child img
            border-color: var(--anzhiyu-blue);

      .reward-main-btn
        background: var(--anzhiyu-secondbg);
        color: var(--anzhiyu-fontcolor);
        display: flex;
        flex-direction: column;
        border-radius: 12px;
        padding: 4px 0;
        border: var(--style-border-always);
        margin: 8px;
        width: 100%;
        &:hover 
          color: var(--anzhiyu-white);
          background: var(--anzhiyu-red);
          box-shadow: var(--anzhiyu-shadow-red);
        .reward-text
          margin-bottom: 0rem;
          font-weight: bold;
        .reward-dec
          font-size: 12px;
.reward-link.mode
  background: var(--anzhiyu-green);
  color: var(--anzhiyu-white);
  padding: 0;
  width: 173px;
  height: 40px;
  line-height: 39px;
  box-shadow: var(--anzhiyu-shadow-green);
  border-radius: 8px;
  text-align: center;
  transition: 0.3s;
  &:hover
    background: var(--anzhiyu-theme);
    box-shadow: none;
  a
    color: var(--anzhiyu-white);
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  i
    margin-right: 4px;

#quit-box
  position: fixed;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, .2);
  top: 0;
  left: 0;
  display: none;
  z-index: 101;
  margin: 0;