# Hexo 博客追番页面配置与自动化部署教程

## 📋 目录
- [功能概述](#功能概述)
- [手动配置追番页面](#手动配置追番页面)
- [GitHub Actions 自动化部署](#github-actions-自动化部署)
- [追番列表管理](#追番列表管理)
- [故障排除](#故障排除)
- [高级配置](#高级配置)

## 🎯 功能概述

本教程将帮你实现：
- ✅ 基于 B站数据的追番页面
- ✅ GitHub Actions 自动化部署
- ✅ 本地修改直接影响网站显示
- ✅ 支持多种追番状态管理
- ✅ 响应式设计，移动端友好

### 工作流程
```
B站追番列表 → 本地配置修改 → GitHub Push → 自动部署 → 网站更新
     ↓              ↓              ↓           ↓          ↓
   数据源         配置文件        触发构建     服务器同步   用户访问
```

## 🚀 手动配置追番页面

### 第一步：安装追番插件

连接到服务器并安装插件：

```bash
# SSH 连接到服务器
ssh root@************

# 进入博客系统目录
cd /opt/blog-system

# 安装追番插件
npm install hexo-bilibili-bangumi --save
```

### 第二步：配置 _config.yml

在博客根目录的 `_config.yml` 文件末尾添加追番配置：

```yaml
# 追番插件配置
# https://github.com/HCLonely/hexo-bilibili-bangumi
bangumi: # 追番设置
  enable: true
  source: bili
  path:
  vmid: 372204786  # 你的B站用户ID
  title: "追番列表"
  quote: "生命不息，追番不止！"
  show: 1          # 1=全部, 2=想看, 3=在看, 4=看过
  lazyload: false
  loading:
  showMyComment: false
  pagination: false
  metaColor:
  color:
  webp:
  progress:
  extraOrder:
  proxy:
    host: "代理host"
    port: "代理端口"
  extra_options:
    top_img: false
    lazyload:
      enable: false
```

### 第三步：生成追番数据

```bash
# 获取B站追番数据
hexo bangumi -u

# 清理并重新生成静态文件
hexo clean && hexo generate

# 部署到网站目录
cp -r public/bangumis /var/www/blog/
chown -R deploy:deploy /var/www/blog/bangumis
```

### 第四步：验证部署

访问追番页面：
- **HTTP**: http://myblog.xing2006.me/bangumis/
- **HTTPS**: https://blog.xing2006.me/bangumis/
- **IP直接访问**: http://************/bangumis/

## 🤖 GitHub Actions 自动化部署

### 配置自动化追番更新

创建 `.github/workflows/update-bangumi.yml` 文件：

```yaml
name: Update Bangumi & Deploy

on:
  # 手动触发
  workflow_dispatch:
  
  # 定时更新（每天凌晨2点）
  schedule:
    - cron: '0 2 * * *'
  
  # 推送到main分支时触发
  push:
    branches: ["main", "master"]
    paths:
      - '_config.yml'
      - '_config.anzhiyu.yml'
      - 'source/**'

env:
  SSH_HOST: ${{ secrets.SSH_HOST }}
  SSH_USER: ${{ secrets.SSH_USER }}
  SSH_PORT: ${{ secrets.SSH_PORT }}
  TARGET_DIR: ${{ secrets.TARGET_DIR }}

jobs:
  update-bangumi-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: false

      # 设置 Node.js 环境
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'anzhiyu-blog/package-lock.json'

      # 安装依赖
      - name: Install dependencies
        run: |
          cd anzhiyu-blog
          npm ci

      # 安装追番插件
      - name: Install bangumi plugin
        run: |
          cd anzhiyu-blog
          npm install hexo-bilibili-bangumi --save

      # 更新追番数据
      - name: Update bangumi data
        run: |
          cd anzhiyu-blog
          npx hexo bangumi -u
        continue-on-error: true  # 即使追番更新失败也继续构建

      # 构建博客
      - name: Build blog
        run: |
          cd anzhiyu-blog
          npm run clean && npm run build

      # 部署到服务器
      - name: Deploy to server
        uses: burnett01/rsync-deployments@6.0.0
        with:
          switches: -avzr --delete --exclude='.git*' --exclude='node_modules'
          path: anzhiyu-blog/public/
          remote_path: ${{ secrets.TARGET_DIR }}
          remote_host: ${{ secrets.SSH_HOST }}
          remote_port: ${{ secrets.SSH_PORT }}
          remote_user: ${{ secrets.SSH_USER }}
          remote_key: ${{ secrets.SSH_PRIVATE_KEY }}

      # 发送通知（可选）
      - name: Notification
        if: success()
        run: |
          echo "✅ 追番页面更新并部署成功！"
          echo "🌐 访问地址: https://blog.xing2006.me/bangumis/"
```

### 配置 GitHub Secrets

在 GitHub 仓库的 Settings → Secrets and variables → Actions 中确保已配置：

| 密钥名称 | 说明 | 示例值 |
|---------|------|--------|
| `SSH_HOST` | 服务器IP地址 | `************` |
| `SSH_USER` | SSH用户名 | `deploy` |
| `SSH_PORT` | SSH端口 | `22` |
| `TARGET_DIR` | 目标目录 | `/var/www/blog` |
| `SSH_PRIVATE_KEY` | SSH私钥 | `-----BEGIN OPENSSH PRIVATE KEY-----...` |

## 📱 追番列表管理

### 在B站修改追番列表

1. **登录B站账号**（用户ID: 372204786）
2. **进入个人空间** → **追番/追剧**页面
3. **管理追番状态**：
   - 添加番剧：搜索 → 点击"追番"
   - 移除番剧：追番列表 → "取消追番"
   - 修改状态：标记为"想看"、"在看"、"看过"

### 本地配置修改

#### 修改显示设置

编辑 `anzhiyu-blog/_config.yml`：

```yaml
bangumi:
  enable: true
  vmid: 372204786
  title: "我的追番列表"        # 自定义页面标题
  quote: "二次元世界，永远的家！"  # 自定义座右铭
  show: 1                     # 1=全部, 2=想看, 3=在看, 4=看过
  showMyComment: true         # 显示个人评价
  pagination: true            # 启用分页
```

#### 修改样式配置

编辑 `anzhiyu-blog/_config.anzhiyu.yml`：

```yaml
# 追番页面样式配置
bangumi:
  top_img: https://example.com/bangumi-bg.jpg  # 自定义背景图
  card_style: true                             # 卡片样式
  show_count: true                             # 显示数量统计
```

### 自动化更新流程

#### 方法一：推送触发更新

```bash
# 1. 修改配置文件
cd anzhiyu-blog
nano _config.yml  # 或 _config.anzhiyu.yml

# 2. 提交并推送
git add .
git commit -m "feat: 更新追番页面配置"
git push origin main

# 3. GitHub Actions 自动执行：
#    - 更新B站追番数据
#    - 重新构建博客
#    - 部署到服务器
```

#### 方法二：手动触发更新

1. 访问 GitHub 仓库
2. 点击 **Actions** 标签
3. 选择 "Update Bangumi & Deploy" workflow
4. 点击 **Run workflow** 按钮

#### 方法三：定时自动更新

配置文件中的定时任务会：
- 每天凌晨2点自动运行
- 获取最新的B站追番数据
- 重新部署到网站

## 🔧 故障排除

### 常见问题

#### 1. 追番数据获取失败

```bash
# 检查B站用户ID是否正确
# 检查追番列表是否公开
# 检查网络连接

# 手动测试
cd anzhiyu-blog
npx hexo bangumi -u --debug
```

#### 2. 页面显示异常

```bash
# 检查生成的文件
ls -la public/bangumis/

# 检查文件权限
ls -la /var/www/blog/bangumis/

# 重新生成
hexo clean && hexo generate
```

#### 3. GitHub Actions 失败

检查 Actions 日志中的错误信息：
- SSH 连接问题
- 依赖安装失败
- 构建过程错误
- 部署权限问题

### 调试命令

```bash
# 本地调试
cd anzhiyu-blog
npm install hexo-bilibili-bangumi --save
npx hexo bangumi -u --debug
npx hexo generate --debug
npx hexo server

# 服务器调试
ssh root@************
cd /opt/blog-system
tail -f /var/log/nginx/myblog.xing2006.me.access.log
```

## ⚙️ 高级配置

### 自定义追番页面模板

创建 `anzhiyu-blog/source/bangumis/index.md`：

```markdown
---
title: 追番列表
date: 2025-08-23 12:00:00
type: bangumis
top_img: https://example.com/bangumi-bg.jpg
comments: false
---

欢迎来到我的追番世界！这里记录了我正在追的番剧和已经看过的经典作品。

<!-- 追番列表将自动生成在这里 -->
```

### 多用户追番支持

```yaml
# 支持多个B站用户的追番
bangumi:
  enable: true
  users:
    - vmid: 372204786
      name: "主账号"
      title: "我的追番"
    - vmid: 123456789
      name: "备用账号"
      title: "备用追番"
```

### CDN 加速配置

```yaml
# 使用CDN加速番剧封面图片
bangumi:
  enable: true
  cdn:
    enable: true
    host: "https://cdn.example.com"
    path: "/bangumi/"
```

## 📊 监控和统计

### 访问统计

在 Google Analytics 或其他统计工具中添加追番页面监控：

```javascript
// 追番页面访问统计
gtag('config', 'GA_MEASUREMENT_ID', {
  page_title: '追番列表',
  page_location: 'https://blog.xing2006.me/bangumis/'
});
```

### 自动化报告

创建定期报告脚本：

```bash
#!/bin/bash
# bangumi-report.sh
echo "=== 追番页面状态报告 ==="
echo "更新时间: $(date)"
echo "页面访问: $(curl -s https://blog.xing2006.me/bangumis/ | wc -c) bytes"
echo "追番数量: $(grep -o 'bangumi-item' /var/www/blog/bangumis/index.html | wc -l)"
```

---

## 📋 快速操作指南

### 日常更新流程

```bash
# 1. 在B站修改追番列表
# 2. 本地修改配置（如需要）
git add _config.yml
git commit -m "update: 追番配置调整"
git push origin main
# 3. 等待自动部署完成
# 4. 访问 https://blog.xing2006.me/bangumis/ 查看结果
```

### 紧急修复流程

```bash
# 1. 手动连接服务器
ssh root@************
cd /opt/blog-system

# 2. 手动更新
hexo bangumi -u
hexo clean && hexo generate
cp -r public/bangumis /var/www/blog/
chown -R deploy:deploy /var/www/blog/bangumis

# 3. 验证修复
curl -I https://blog.xing2006.me/bangumis/
```

## 🎨 自定义样式配置

### 追番页面样式定制

创建自定义CSS文件 `anzhiyu-blog/source/css/bangumi-custom.css`：

```css
/* 追番页面自定义样式 */
.bangumi-page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.bangumi-card {
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.bangumi-card:hover {
  transform: translateY(-5px);
}

.bangumi-status {
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
}

.bangumi-progress {
  background: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
}

.bangumi-progress-bar {
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  height: 6px;
  transition: width 0.5s ease;
}
```

### 响应式设计配置

```css
/* 移动端适配 */
@media (max-width: 768px) {
  .bangumi-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .bangumi-card {
    padding: 15px;
  }

  .bangumi-title {
    font-size: 14px;
    line-height: 1.4;
  }
}

@media (max-width: 480px) {
  .bangumi-grid {
    grid-template-columns: 1fr;
  }
}
```

## 🔄 版本控制与回滚

### 配置版本管理

```bash
# 创建配置备份分支
git checkout -b config-backup
git push origin config-backup

# 在主分支进行配置修改
git checkout main
# 修改配置...
git add _config.yml
git commit -m "feat: 追番页面配置优化"
git push origin main
```

### 快速回滚机制

```bash
# 如果新配置有问题，快速回滚
git revert HEAD
git push origin main

# 或者回滚到指定版本
git reset --hard <commit-hash>
git push --force origin main
```

### 配置文件版本对比

```bash
# 查看配置文件变更历史
git log --oneline -- _config.yml

# 对比不同版本的配置
git diff HEAD~1 HEAD -- _config.yml

# 查看特定提交的配置内容
git show <commit-hash>:_config.yml
```

## 📈 性能优化

### 图片懒加载配置

```yaml
bangumi:
  enable: true
  lazyload: true
  loading: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iI2RkZCIvPgo8dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1zaXplPSIxOCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iIGZpbGw9IiM5OTkiPkxvYWRpbmcuLi48L3RleHQ+Cjwvc3ZnPg=="
```

### CDN 配置优化

```yaml
bangumi:
  enable: true
  webp: true  # 启用WebP格式
  proxy:      # 使用代理加速
    host: "proxy.example.com"
    port: "8080"
```

### 缓存策略配置

在 Nginx 配置中添加追番页面缓存：

```nginx
# 追番页面缓存配置
location /bangumis/ {
    expires 1h;
    add_header Cache-Control "public, no-transform";

    # 静态资源长期缓存
    location ~* \.(jpg|jpeg|png|gif|webp)$ {
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
}
```

## 🛡️ 安全配置

### API 访问限制

```yaml
bangumi:
  enable: true
  extra_options:
    rate_limit: 10        # 每分钟最多10次请求
    timeout: 30000        # 30秒超时
    retry: 3              # 失败重试3次
    user_agent: "Custom-Bangumi-Bot/1.0"
```

### 数据验证配置

```yaml
bangumi:
  enable: true
  validation:
    min_score: 0          # 最低评分
    max_score: 10         # 最高评分
    required_fields:      # 必需字段
      - title
      - cover
      - url
```

## 📊 数据分析与统计

### 追番统计配置

```yaml
bangumi:
  enable: true
  statistics:
    enable: true
    show_total: true      # 显示总数
    show_status: true     # 显示各状态统计
    show_rating: true     # 显示评分统计
    show_year: true       # 显示年份统计
```

### 自定义统计页面

创建 `anzhiyu-blog/source/bangumi-stats/index.md`：

```markdown
---
title: 追番统计
date: 2025-08-23 12:00:00
type: bangumi-stats
---

## 📊 我的追番数据

### 总体统计
- 总追番数：{{ bangumi.total }}
- 已完成：{{ bangumi.finished }}
- 正在追：{{ bangumi.watching }}
- 计划观看：{{ bangumi.planned }}

### 年度统计
{% for year in bangumi.years %}
- {{ year.name }}：{{ year.count }} 部
{% endfor %}

### 评分分布
{% for rating in bangumi.ratings %}
- {{ rating.score }} 分：{{ rating.count }} 部
{% endfor %}
```

## 🔔 通知与监控

### 微信通知配置

```yaml
# GitHub Actions 中添加微信通知
- name: WeChat Notification
  if: success()
  uses: chf007/action-wechat-work@master
  env:
    WECHAT_WORK_BOT_WEBHOOK: ${{ secrets.WECHAT_WEBHOOK }}
  with:
    msgtype: markdown
    content: |
      ## 追番页面更新成功 ✅

      **更新时间**: ${{ steps.date.outputs.date }}
      **访问地址**: [https://blog.xing2006.me/bangumis/](https://blog.xing2006.me/bangumis/)
      **更新内容**:
      - 同步最新B站追番数据
      - 重新生成静态页面
      - 部署到生产环境
```

### 邮件通知配置

```yaml
- name: Email Notification
  if: failure()
  uses: dawidd6/action-send-mail@v3
  with:
    server_address: smtp.gmail.com
    server_port: 587
    username: ${{ secrets.EMAIL_USERNAME }}
    password: ${{ secrets.EMAIL_PASSWORD }}
    subject: "追番页面部署失败通知"
    body: |
      追番页面自动部署失败，请检查以下内容：
      1. B站API访问是否正常
      2. 服务器连接是否正常
      3. 构建过程是否有错误

      详细日志请查看：${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
    to: <EMAIL>
    from: <EMAIL>
```

## 🧪 测试与验证

### 自动化测试配置

创建 `.github/workflows/test-bangumi.yml`：

```yaml
name: Test Bangumi Page

on:
  pull_request:
    paths:
      - '_config.yml'
      - 'source/bangumis/**'

jobs:
  test-bangumi:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'anzhiyu-blog/package-lock.json'

      - name: Install dependencies
        run: |
          cd anzhiyu-blog
          npm ci
          npm install hexo-bilibili-bangumi --save

      - name: Test bangumi generation
        run: |
          cd anzhiyu-blog
          npx hexo bangumi -u --test
          npx hexo generate

      - name: Validate generated files
        run: |
          cd anzhiyu-blog
          test -f public/bangumis/index.html
          grep -q "bangumi-item" public/bangumis/index.html
          echo "✅ 追番页面生成测试通过"
```

### 本地测试脚本

创建 `test-bangumi.sh`：

```bash
#!/bin/bash
set -e

echo "🧪 开始追番页面测试..."

cd anzhiyu-blog

# 安装依赖
echo "📦 安装依赖..."
npm install

# 安装追番插件
echo "🔌 安装追番插件..."
npm install hexo-bilibili-bangumi --save

# 测试追番数据获取
echo "📡 测试追番数据获取..."
npx hexo bangumi -u --debug

# 生成静态文件
echo "🏗️ 生成静态文件..."
npx hexo clean && npx hexo generate

# 验证生成结果
echo "✅ 验证生成结果..."
if [ -f "public/bangumis/index.html" ]; then
    echo "✅ 追番页面生成成功"

    # 检查页面内容
    if grep -q "bangumi-item" public/bangumis/index.html; then
        echo "✅ 追番数据加载成功"
    else
        echo "❌ 追番数据加载失败"
        exit 1
    fi
else
    echo "❌ 追番页面生成失败"
    exit 1
fi

# 启动本地服务器测试
echo "🌐 启动本地服务器..."
npx hexo server --port 4000 &
SERVER_PID=$!

# 等待服务器启动
sleep 5

# 测试页面访问
echo "🔍 测试页面访问..."
if curl -f http://localhost:4000/bangumis/ > /dev/null 2>&1; then
    echo "✅ 追番页面访问正常"
else
    echo "❌ 追番页面访问失败"
    kill $SERVER_PID
    exit 1
fi

# 清理
kill $SERVER_PID
echo "🎉 所有测试通过！"
```

## 📚 常见问题解答

### Q1: 追番数据不更新怎么办？

**A1**: 检查以下几点：
1. B站用户ID是否正确
2. 追番列表是否设为公开
3. 网络连接是否正常
4. 是否触发了B站的反爬虫机制

```bash
# 调试命令
cd anzhiyu-blog
npx hexo bangumi -u --debug --force
```

### Q2: 页面样式显示异常？

**A2**: 可能的原因：
1. CSS文件缓存问题
2. 主题版本不兼容
3. 自定义样式冲突

```bash
# 清理缓存重新生成
hexo clean && hexo generate
# 强制刷新浏览器缓存 Ctrl+F5
```

### Q3: GitHub Actions 部署失败？

**A3**: 常见解决方案：
1. 检查 SSH 密钥配置
2. 验证服务器连接
3. 查看详细错误日志

```bash
# 本地测试SSH连接
ssh -i ~/.ssh/id_rsa deploy@************
```

### Q4: 如何自定义追番页面布局？

**A4**: 修改主题模板文件：
1. 复制 `themes/anzhiyu/layout/bangumi.ejs`
2. 修改为自定义布局
3. 重新生成部署

### Q5: 支持多个B站账号吗？

**A5**: 目前插件主要支持单账号，但可以通过配置实现：

```yaml
bangumi:
  enable: true
  vmid: 372204786  # 主账号
  extra_users:     # 额外账号（需要自定义实现）
    - vmid: 123456789
      name: "备用账号"
```

---

## 📋 检查清单

### 初始配置检查
- [ ] 追番插件安装成功
- [ ] _config.yml 配置正确
- [ ] B站用户ID有效
- [ ] 追番列表公开可访问
- [ ] 首次数据获取成功
- [ ] 页面生成正常
- [ ] 部署到服务器成功

### GitHub Actions 配置检查
- [ ] workflow 文件创建
- [ ] GitHub Secrets 配置完整
- [ ] SSH 连接测试通过
- [ ] 自动部署测试成功
- [ ] 通知配置正常（可选）

### 日常维护检查
- [ ] 定时更新正常运行
- [ ] 页面访问速度正常
- [ ] 移动端显示正常
- [ ] 数据统计准确
- [ ] 错误日志监控

---

*最后更新：2025年8月23日*

> 💡 **总结**: 通过本教程，你可以实现完全自动化的追番页面管理。只需要在B站管理你的追番列表，或者修改本地配置文件并推送到GitHub，网站就会自动更新。整个过程无需手动登录服务器，真正实现了"一次配置，终身受益"的自动化部署体验！

> 🚀 **下一步**: 建议配置监控和通知功能，这样你就能及时了解部署状态，确保追番页面始终保持最新状态。
