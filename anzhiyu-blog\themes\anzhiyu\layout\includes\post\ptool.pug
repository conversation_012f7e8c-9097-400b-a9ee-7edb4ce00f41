- let url = page.copyright_url || page.permalink
if !theme.disable_top_img && page.top_img !== false
  if is_post()
    - var top_img = page.top_img || page.cover || page.randomcover
  else if is_page()
    - var top_img = page.top_img || theme.default_top_img
  else if is_home()
    - var top_img = theme.index_img !== false ? theme.index_img || theme.default_top_img : false
  else
    - var top_img = page.top_img || theme.default_top_img

  if top_img !== false
    - var imgSource = top_img && top_img.indexOf('/') !== -1 ? url_for(top_img) : top_img
    - var bg_img = top_img ? imgSource : ''
    - var site_title = page.title || page.tag || page.category || config.title
#post-tools.post-tools
  .post-tools-left
    div.rewardLeftButton
      if theme.reward.enable && theme.reward.QR_code
          !=partial('includes/post/reward', {}, {cache: true})
      if theme.ptool.mode
        .reward-link.mode
          a.reward-link-button(href=url_for(theme.ptool.mode))
            i.anzhiyufont.anzhiyu-icon-plant-fill
            | 运营模式与责任
    div.shareRight
      if theme.ptool.share_mobile
        .share-link.mobile
          .share-qrcode
            .share-button(title='使用手机访问这篇文章')
              i.anzhiyufont.anzhiyu-icon-qrcode
            .share-main
              .share-main-all
                #qrcode(title=url)
                .reward-dec 使用手机访问这篇文章
      if theme.ptool.share_weibo
        .share-link.weibo
          a.share-button(target='_blank' href=`https://service.weibo.com/share/share.php?title=${site_title}&url=${url}&pic=${bg_img}` rel='external nofollow noreferrer noopener')
            i.anzhiyufont.anzhiyu-icon-weibo
      if theme.ptool.share_copyurl
        script.
          function copyCurrentPageUrl() {
            var currentPageUrl = window.location.href;
            var input = document.createElement("input");
            input.setAttribute("value", currentPageUrl);
            document.body.appendChild(input);
            input.select();
            input.setSelectionRange(0, 99999);
            document.execCommand("copy");
            document.body.removeChild(input);
          }
        .share-link.copyurl
          #post-share-url.share-button(title='复制链接' onclick='copyCurrentPageUrl()')
            i.anzhiyufont.anzhiyu-icon-link
