- loading_img = theme.preloader.avatar ? theme.preloader.avatar : theme.avatar.img
#loading-box(onclick='document.getElementById("loading-box").classList.add("loaded")')
  .loading-bg
    img.loading-img(alt="加载头像" class='nolazyload' src=url_for(loading_img))
    .loading-image-dot
script.
  const preloader = {
    endLoading: () => {
      document.getElementById('loading-box').classList.add("loaded");
    },
    initLoading: () => {
      document.getElementById('loading-box').classList.remove("loaded")
    }
  }
  window.addEventListener('load',()=> { preloader.endLoading() })
  setTimeout(function(){preloader.endLoading();},10000)

  if (!{theme.pjax && theme.pjax.enable}) {
    document.addEventListener('pjax:send', () => { preloader.initLoading() })
    document.addEventListener('pjax:complete', () => { preloader.endLoading() })
  }
