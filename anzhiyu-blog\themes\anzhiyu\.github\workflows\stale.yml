name: 'Close stale issues and PRs'
on:
  schedule:
    - cron: '30 1 * * *'

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v5
        with:
          days-before-issue-stale: 30
          days-before-pr-stale: -1
          days-before-close: 7
          stale-issue-message: 'This issue has been automatically marked as stale because it has not had recent activity. It will be closed if no further activity occurs. Thank you for your contributions.'
          close-pr-message: 'This issue has not seen any activity since it was marked stale. Closing.'
          stale-issue-label: 'Stale'
          exempt-issue-labels: 'pinned,bug,enhancement,documentation,Plan'
          operations-per-run: 1000